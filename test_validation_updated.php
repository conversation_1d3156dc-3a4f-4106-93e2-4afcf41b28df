<?php
/**
 * 测试 changeItemsInfo 方法的验证规则（包含新的金额验证）
 */

// 模拟测试数据
$test_cases = [
    // 测试 type=1 (添加赠品)
    [
        'name' => 'type=1 添加赠品 - 正常情况',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 1,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 2,
                    'price' => 0
                ]
            ]
        ],
        'should_pass' => true
    ],
    
    // 测试 type=3 (补发商品) - 金额不为0（应该失败）
    [
        'name' => 'type=3 补发商品 - 金额不为0（应该失败）',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 3,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 1,
                    'price' => 100
                ]
            ],
            'full_address' => '福建省厦门市湖里区湖里街道*****',
            'consignee' => '焦*埼',
            'consignee_phone' => '181****5055',
            'express_type' => 2
        ],
        'should_pass' => false,
        'expected_error' => '补发商品金额只能为0'
    ],
    
    // 测试 type=3 (补发商品) - 金额为0（正确）
    [
        'name' => 'type=3 补发商品 - 金额为0（正确）',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 3,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 1,
                    'price' => 0
                ]
            ],
            'full_address' => '福建省厦门市湖里区湖里街道*****',
            'consignee' => '焦*埼',
            'consignee_phone' => '181****5055',
            'express_type' => 2
        ],
        'should_pass' => true
    ],
    
    // 测试 type=4 (商品换货) - 正常情况
    [
        'name' => 'type=4 商品换货 - 正常情况',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 4,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 1,
                    'price' => 50.00
                ]
            ],
            'full_address' => '福建省厦门市湖里区湖里街道*****',
            'consignee' => '焦*埼',
            'consignee_phone' => '181****5055',
            'express_type' => 1
        ],
        'should_pass' => true,
        'note' => '需要验证换货金额不超过退货金额'
    ],
    
    // 测试 type=4 (商品换货) - 金额为0
    [
        'name' => 'type=4 商品换货 - 金额为0',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 4,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 1,
                    'price' => 0
                ]
            ],
            'full_address' => '福建省厦门市湖里区湖里街道*****',
            'consignee' => '焦*埼',
            'consignee_phone' => '181****5055',
            'express_type' => 1
        ],
        'should_pass' => true,
        'note' => '换货金额为0时不需要验证退货金额'
    ],
    
    // 测试缺少必填字段
    [
        'name' => 'type=3 缺少收货地址',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 3,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 2,
                    'price' => 0
                ]
            ]
        ],
        'should_pass' => false,
        'expected_error' => '收货地址相关字段必填'
    ],
    
    // 测试无效的 type
    [
        'name' => '无效的 type=5',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 5,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 2,
                    'price' => 0
                ]
            ]
        ],
        'should_pass' => false,
        'expected_error' => 'type字段值无效'
    ],
    
    // 测试商品数量为0
    [
        'name' => '商品数量为0',
        'data' => [
            'admin_id' => 123,
            'sub_order_no' => '110178575845226400',
            'type' => 1,
            'product' => [
                [
                    'short_code' => 'GWL23-21H',
                    'nums' => 0,
                    'price' => 0
                ]
            ]
        ],
        'should_pass' => false,
        'expected_error' => '商品数量必须大于0'
    ]
];

echo "验证规则测试用例（包含新的金额验证）:\n";
echo "==========================================\n\n";

foreach ($test_cases as $i => $case) {
    echo ($i + 1) . ". " . $case['name'] . "\n";
    echo "   数据: " . json_encode($case['data'], JSON_UNESCAPED_UNICODE) . "\n";
    echo "   预期: " . ($case['should_pass'] ? '通过' : '失败') . "\n";
    if (isset($case['expected_error'])) {
        echo "   预期错误: " . $case['expected_error'] . "\n";
    }
    if (isset($case['note'])) {
        echo "   备注: " . $case['note'] . "\n";
    }
    echo "\n";
}

echo "主要验证规则总结:\n";
echo "================\n";
echo "1. type 支持 1,2,3,4 四种类型\n";
echo "2. type=1,2 时只需要基本字段\n";
echo "3. type=3,4 时需要额外的收货地址字段:\n";
echo "   - full_address (必填)\n";
echo "   - consignee (必填)\n";
echo "   - consignee_phone (必填)\n";
echo "   - express_type (必填)\n";
echo "4. confirm 字段对 type=3 有效，可选\n";
echo "5. 商品数量必须大于0\n";
echo "6. 商品价格必须大于等于0\n";
echo "7. 【新增】补发商品（type=3）金额只能为0\n";
echo "8. 【新增】换货商品（type=4）金额最大值为退货金额\n";
echo "   - 计算公式：可用金额 = 订单支付金额 - 已退货金额\n";
echo "   - 换货总金额不能超过可用金额\n";
